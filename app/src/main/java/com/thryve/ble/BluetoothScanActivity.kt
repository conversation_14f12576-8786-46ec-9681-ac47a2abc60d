package com.thryve.ble

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.widget.ArrayAdapter
import android.widget.ListView
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class BluetoothScanActivity : Activity() {
    private val REQUEST_ENABLE_BT = 1
    private val REQUEST_PERMISSIONS = 2
    private lateinit var bluetoothAdapter: BluetoothAdapter
    private lateinit var deviceListAdapter: ArrayAdapter<String>
    private val foundDevices = mutableListOf<BluetoothDevice>()
    private val handler = Handler()
    private var scanning = false
    private val SCAN_PERIOD: Long = 10000 // 10 seconds

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val listView = ListView(this)
        setContentView(listView)

        deviceListAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1)
        listView.adapter = deviceListAdapter

        val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothAdapter = bluetoothManager.adapter

        if (bluetoothAdapter == null) {
            Toast.makeText(this, "Bluetooth not supported", Toast.LENGTH_LONG).show()
            finish()
            return
        }

        // Check permissions first, then enable Bluetooth if needed
        checkPermissionsAndEnableBluetooth()

        listView.setOnItemClickListener { _, _, position, _ ->
            val device = foundDevices[position]
            connectToDevice(device)
        }
    }

    private fun checkPermissionsAndEnableBluetooth() {
        val permissions = mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions.add(Manifest.permission.BLUETOOTH_SCAN)
            permissions.add(Manifest.permission.BLUETOOTH_CONNECT)
        }
        val missing = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        if (missing.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, missing.toTypedArray(), REQUEST_PERMISSIONS)
        } else {
            enableBluetoothIfNeeded()
        }
    }

    private fun enableBluetoothIfNeeded() {
        if (!bluetoothAdapter.isEnabled) {
            // Check if we have BLUETOOTH_CONNECT permission for Android 12+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "BLUETOOTH_CONNECT permission required to enable Bluetooth", Toast.LENGTH_LONG).show()
                finish()
                return
            }
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
        } else {
            startScan()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_PERMISSIONS) {
            if (grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                enableBluetoothIfNeeded()
            } else {
                Toast.makeText(this, "Permissions required for Bluetooth scanning", Toast.LENGTH_LONG).show()
                finish()
            }
        }
    }

    private fun startScan() {
        if (scanning) return
        deviceListAdapter.clear()
        foundDevices.clear()
        scanning = true
        handler.postDelayed({
            scanning = false
            bluetoothAdapter.cancelDiscovery()
        }, SCAN_PERIOD)
        bluetoothAdapter.startDiscovery()
        registerReceiver(
            object : android.content.BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    if (BluetoothDevice.ACTION_FOUND == intent?.action) {
                        val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                        device?.let {
                            if (!foundDevices.contains(it)) {
                                foundDevices.add(it)
                                deviceListAdapter.add("${it.name ?: "Unknown"} - ${it.address}")
                            }
                        }
                    }
                }
            },
            android.content.IntentFilter(BluetoothDevice.ACTION_FOUND)
        )
    }

    private fun connectToDevice(device: BluetoothDevice) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(this, "BLUETOOTH_CONNECT permission required", Toast.LENGTH_SHORT).show()
            return
        }
        AlertDialog.Builder(this)
            .setTitle("Connect")
            .setMessage("Request to connect to ${device.name ?: device.address}.")
            .setPositiveButton("Connect") { _, _ ->
                // For classic Bluetooth, you would use device.createRfcommSocketToServiceRecord(UUID)
                // For BLE, you would use device.connectGatt(...)
                Toast.makeText(this, "Connection requested (implement actual connection logic)", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                startScan()
            } else {
                Toast.makeText(this, "Bluetooth is required", Toast.LENGTH_LONG).show()
                finish()
            }
        }
    }
} 