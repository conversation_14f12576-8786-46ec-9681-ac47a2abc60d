package com.thryve.ble

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.*
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class BluetoothScanActivity : Activity() {
    private val REQUEST_ENABLE_BT = 1
    private val REQUEST_PERMISSIONS = 2
    private lateinit var bluetoothAdapter: BluetoothAdapter
    private lateinit var deviceListAdapter: ArrayAdapter<String>
    private val foundDevices = mutableListOf<BluetoothDevice>()
    private val handler = Handler()
    private var scanning = false
    private val SCAN_PERIOD: Long = 10000 // 10 seconds

    // UI Components
    private lateinit var scanButton: Button
    private lateinit var stopScanButton: Button
    private lateinit var clearButton: Button
    private lateinit var statusText: TextView
    private lateinit var deviceListView: ListView
    private var deviceReceiver: BroadcastReceiver? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupUI()

        val bluetoothManager = getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothAdapter = bluetoothManager.adapter

        if (bluetoothAdapter == null) {
            Toast.makeText(this, "Bluetooth not supported", Toast.LENGTH_LONG).show()
            finish()
            return
        }

        // Check permissions first, then enable Bluetooth if needed
        checkPermissionsAndEnableBluetooth()

        deviceListView.setOnItemClickListener { _, _, position, _ ->
            if (position < foundDevices.size) {
                val device = foundDevices[position]
                connectToDevice(device)
            }
        }
    }

    private fun setupUI() {
        // Create main layout
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Status text
        statusText = TextView(this).apply {
            text = "Ready to scan for Bluetooth devices"
            textSize = 16f
            setPadding(0, 0, 0, 16)
        }

        // Button layout
        val buttonLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        // Scan button
        scanButton = Button(this).apply {
            text = "Start Scan"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setOnClickListener { startScan() }
        }

        // Stop scan button
        stopScanButton = Button(this).apply {
            text = "Stop Scan"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            isEnabled = false
            setOnClickListener { stopScan() }
        }

        // Clear button
        clearButton = Button(this).apply {
            text = "Clear List"
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setOnClickListener { clearDeviceList() }
        }

        buttonLayout.addView(scanButton)
        buttonLayout.addView(stopScanButton)
        buttonLayout.addView(clearButton)

        // Device list
        deviceListView = ListView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }

        deviceListAdapter = ArrayAdapter(this, android.R.layout.simple_list_item_1)
        deviceListView.adapter = deviceListAdapter

        // Add all views to main layout
        mainLayout.addView(statusText)
        mainLayout.addView(buttonLayout)
        mainLayout.addView(deviceListView)

        setContentView(mainLayout)
    }

    private fun checkPermissionsAndEnableBluetooth() {
        val permissions = mutableListOf(
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions.add(Manifest.permission.BLUETOOTH_SCAN)
            permissions.add(Manifest.permission.BLUETOOTH_CONNECT)
        }
        val missing = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        if (missing.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, missing.toTypedArray(), REQUEST_PERMISSIONS)
        } else {
            enableBluetoothIfNeeded()
        }
    }

    private fun enableBluetoothIfNeeded() {
        if (!bluetoothAdapter.isEnabled) {
            // Check if we have BLUETOOTH_CONNECT permission for Android 12+
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "BLUETOOTH_CONNECT permission required to enable Bluetooth", Toast.LENGTH_LONG).show()
                finish()
                return
            }
            statusText.text = "Enabling Bluetooth..."
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT)
        } else {
            statusText.text = "Bluetooth enabled. Ready to scan."
            updateUI()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_PERMISSIONS) {
            if (grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                enableBluetoothIfNeeded()
            } else {
                Toast.makeText(this, "Permissions required for Bluetooth scanning", Toast.LENGTH_LONG).show()
                finish()
            }
        }
    }

    private fun startScan() {
        if (scanning) {
            Toast.makeText(this, "Scan already in progress", Toast.LENGTH_SHORT).show()
            return
        }

        // Check permissions before scanning
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(this, "BLUETOOTH_SCAN permission required", Toast.LENGTH_SHORT).show()
            return
        }

        scanning = true
        updateUI()
        statusText.text = "Scanning for devices..."

        // Set up the receiver for found devices
        deviceReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    BluetoothDevice.ACTION_FOUND -> {
                        val device: BluetoothDevice? = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                        device?.let { addDeviceToList(it) }
                    }
                    BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                        scanning = false
                        updateUI()
                        statusText.text = "Scan completed. Found ${foundDevices.size} devices."
                    }
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(BluetoothDevice.ACTION_FOUND)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
        }
        registerReceiver(deviceReceiver, filter)

        // Start discovery
        bluetoothAdapter.startDiscovery()

        // Auto-stop after SCAN_PERIOD
        handler.postDelayed({
            if (scanning) {
                stopScan()
            }
        }, SCAN_PERIOD)
    }

    private fun stopScan() {
        if (!scanning) return

        scanning = false
        bluetoothAdapter.cancelDiscovery()

        deviceReceiver?.let {
            unregisterReceiver(it)
            deviceReceiver = null
        }

        updateUI()
        statusText.text = "Scan stopped. Found ${foundDevices.size} devices."
    }

    private fun clearDeviceList() {
        foundDevices.clear()
        deviceListAdapter.clear()
        statusText.text = "Device list cleared. Ready to scan."
    }

    private fun addDeviceToList(device: BluetoothDevice) {
        if (!foundDevices.contains(device)) {
            foundDevices.add(device)
            val deviceName = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                "Unknown Device"
            } else {
                device.name ?: "Unknown Device"
            }
            val deviceInfo = "$deviceName\n${device.address}"
            deviceListAdapter.add(deviceInfo)
            statusText.text = "Scanning... Found ${foundDevices.size} devices"
        }
    }

    private fun updateUI() {
        scanButton.isEnabled = !scanning
        stopScanButton.isEnabled = scanning
        clearButton.isEnabled = !scanning
    }

    private fun connectToDevice(device: BluetoothDevice) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(this, "BLUETOOTH_CONNECT permission required", Toast.LENGTH_SHORT).show()
            return
        }
        AlertDialog.Builder(this)
            .setTitle("Connect")
            .setMessage("Request to connect to ${device.name ?: device.address}.")
            .setPositiveButton("Connect") { _, _ ->
                // For classic Bluetooth, you would use device.createRfcommSocketToServiceRecord(UUID)
                // For BLE, you would use device.connectGatt(...)
                Toast.makeText(this, "Connection requested (implement actual connection logic)", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_ENABLE_BT) {
            if (resultCode == RESULT_OK) {
                statusText.text = "Bluetooth enabled. Ready to scan."
                updateUI()
            } else {
                Toast.makeText(this, "Bluetooth is required", Toast.LENGTH_LONG).show()
                finish()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopScan()
    }
} 